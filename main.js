const { app, BrowserWindow, ipcMain, session, Menu, globalShortcut, shell, screen, webContents } = require('electron')
const path = require('path')
const fs = require('fs').promises

let downloadWindow = null;
let bookmarksWindow = null;
let downloads = new Map();
// Store completed download paths
let completedDownloadPaths = new Map();
// 添加一个Map来跟踪下载与源窗口的关系
let downloadSourceWindows = new Map();

// 添加一个Set来跟踪弹出窗口
const popupWindowIds = new Set();

// 添加一个标记来专门跟踪通过我们创建的主窗口
const mainWindowIds = new Set();

// 设置常量
const MAX_BOOKMARKS = 1000; // 最大书签数量

// 存储待处理的 URL
let pendingUrl = null;

// 设置书签文件路径
const bookmarksPath = path.join(app.getPath('userData'), 'settings.json')

// 设置密码文件路径
const passwordsPath = path.join(app.getPath('userData'), 'pass.json')

// 在文件顶部添加一个Set来跟踪正在处理的下载
let processingDownloads = new Set();

// 在文件顶部添加一个Set来跟踪已注册下载处理器的webContents ID
let registeredDownloadHandlers = new Set();

// 在文件顶部，其他全局变量之后添加
let isEscapeRegistered = false;

// Create a function to handle Escape key (renamed from global to local)
function handleEscapeKey() {
  // 获取当前焦点窗口
  const win = BrowserWindow.getFocusedWindow();
  if (!win || win.isDestroyed()) {
    return;
  }

  try {
    // 获取所有webContents
    const allWebContents = webContents.getAllWebContents();
    console.log('esc pressed');

    // 找到当前窗口的webview的webContents
    const webviewContents = allWebContents.find(contents =>
      contents.getType() === 'webview' &&
      !contents.isDestroyed() &&
      contents.hostWebContents === win.webContents
    );

    if (webviewContents && !webviewContents.isDestroyed()) {
      // 停止页面加载
      webviewContents.stop();
    }

    // 通知渲染进程处理UI相关的操作
    if (!win.isDestroyed() && win.webContents && !win.webContents.isDestroyed()) {
      win.webContents.send('handle-escape');
    }
  } catch (error) {
    console.error('Error in handleEscapeKey:', error);
  }
}

// 确保书签文件存在
async function ensureBookmarksFile() {
  try {
    await fs.access(bookmarksPath)
    // 检查文件是否为空或格式不正确
    try {
      const data = await fs.readFile(bookmarksPath, 'utf8')
      if (!data.trim()) {
        // 文件为空，写入默认结构
        await fs.writeFile(bookmarksPath, JSON.stringify({ bookmarks: [], settings: {} }, null, 2), 'utf8')
      } else {
        // 尝试解析文件，如果格式不正确则修复
        const jsonData = JSON.parse(data)
        if (Array.isArray(jsonData)) {
          // 旧格式（纯数组），转换为新格式
          const newData = { bookmarks: jsonData, settings: {} }
          await fs.writeFile(bookmarksPath, JSON.stringify(newData, null, 2), 'utf8')
        } else if (!jsonData.bookmarks && !jsonData.settings) {
          // 格式不正确，重置为默认结构
          await fs.writeFile(bookmarksPath, JSON.stringify({ bookmarks: [], settings: {} }, null, 2), 'utf8')
        }
      }
    } catch (parseError) {
      // 文件存在但无法解析，重置为默认结构
      await fs.writeFile(bookmarksPath, JSON.stringify({ bookmarks: [], settings: {} }, null, 2), 'utf8')
    }
  } catch {
    // 如果文件不存在，创建一个包含正确结构的文件
    await fs.writeFile(bookmarksPath, JSON.stringify({ bookmarks: [], settings: {} }, null, 2), 'utf8')
  }
}

// 读取书签
async function readBookmarks() {
  try {
    const data = await fs.readFile(bookmarksPath, 'utf8')
    if (!data.trim()) {
      return []
    }
    
    const jsonData = JSON.parse(data)
    // 如果是新格式，返回 bookmarks 数组
    if (jsonData.bookmarks && Array.isArray(jsonData.bookmarks)) {
      return jsonData.bookmarks
    }
    // 如果是旧格式（纯数组），直接返回
    if (Array.isArray(jsonData)) {
      return jsonData
    }
    // 如果都不是，返回空数组
    return []
  } catch (error) {
    console.error('Error reading bookmarks:', error);
    return []
  }
}

// 保存书签
async function saveBookmarks(bookmarks) {
  try {
    // 读取现有文件以保留其他设置
    console.log('saveBookmarks', bookmarksPath)
    let jsonData = { bookmarks: [], settings: {} };
    
    try {
      const data = await fs.readFile(bookmarksPath, 'utf8')
      if (data.trim()) {  // 只在文件不为空时解析
        jsonData = JSON.parse(data)
      }
    } catch (readError) {
      // 如果文件不存在或为空，使用默认值
      console.log('Creating new settings file for bookmarks');
    }

    // 确保数据结构正确
    if (!jsonData.settings) {
      jsonData.settings = {};
    }
    
    // 更新书签数组
    jsonData.bookmarks = bookmarks;
    
    console.log('saveBookmarks', JSON.stringify(jsonData, null, 2))
    await fs.writeFile(bookmarksPath, JSON.stringify(jsonData, null, 2), 'utf8')
  } catch (error) {
    console.error('Error saving bookmarks:', error);
    // 如果出错，尝试直接保存书签数组（保持向后兼容）
    await fs.writeFile(bookmarksPath, JSON.stringify({ bookmarks: bookmarks, settings: {} }, null, 2), 'utf8')
  }
}

// 设置为默认浏览器
app.setAsDefaultProtocolClient('http');
app.setAsDefaultProtocolClient('https');

// Add event handler for all created windows
app.on('browser-window-created', async (event, window) => {
  // Only apply to popup windows (not main windows which already have opacity applied)
  if (isPopupWindow(window) && !window.isDestroyed()) {
    try {
      const data = await fs.readFile(bookmarksPath, 'utf8');
      const json = JSON.parse(data);
      if (json.settings && json.settings.opacity) {
        // Apply the saved opacity setting to this popup window
        window.setOpacity(json.settings.opacity);
      } else {
        // If no opacity setting exists, apply the default main window opacity
        const mainWindows = BrowserWindow.getAllWindows().filter(win => !isPopupWindow(win));
        if (mainWindows.length > 0 && !mainWindows[0].isDestroyed()) {
          window.setOpacity(mainWindows[0].getOpacity());
        }
      }
    } catch (error) {
      console.error('Error applying opacity to popup window:', error);
    }
  }
});

// 处理外部链接
app.on('open-url', (event, url) => {
  event.preventDefault();
  console.log('Received URL via open-url:', url);
  pendingUrl = url;

  // 如果应用程序还没准备好，等待它准备好
  if (!app.isReady()) {
    // 不在这里创建窗口，让 whenReady 处理程序来创建
    return;
  } else {
    // 如果应用程序已经准备好，总是创建新窗口
    const win = createWindow();
    win.webContents.once('did-finish-load', () => {
      console.log('Loading URL in new window:', url);
      win.webContents.send('load-url', url);
    });
  }
});

// 处理文件打开（HTML文件）
app.on('open-file', (event, filePath) => {
  event.preventDefault();
  console.log('Received file path via open-file:', filePath);
  
  // 检查文件是否为HTML文件
  if (filePath.toLowerCase().endsWith('.html') || filePath.toLowerCase().endsWith('.htm')) {
    // 将文件路径转换为file:// URL
    const fileUrl = `file://${filePath}`;
    console.log('Converting file path to URL:', fileUrl);
    
    // 如果应用程序还没准备好，保存文件URL等待处理
    if (!app.isReady()) {
      pendingUrl = fileUrl;
      return;
    } else {
      // 如果应用程序已经准备好，创建新窗口并加载文件
      const win = createWindow();
      win.webContents.once('did-finish-load', () => {
        console.log('Loading file in new window:', fileUrl);
        win.webContents.send('load-url', fileUrl);
      });
    }
  }
});

// 处理启动参数
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (event, argv) => {
    // 检查是否有 URL 或文件参数
    const url = argv[argv.length - 1];
    console.log('Second instance URL/file:', url);

    let urlToLoad = null;
    if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
      urlToLoad = url;
    } else if (url && (url.toLowerCase().endsWith('.html') || url.toLowerCase().endsWith('.htm'))) {
      // 处理HTML文件路径
      urlToLoad = `file://${url}`;
      console.log('Converting second instance file path to URL:', urlToLoad);
    }

    if (urlToLoad) {
      // 总是创建新窗口
      const win = createWindow();
      win.webContents.once('did-finish-load', () => {
        console.log('Loading URL in new window:', urlToLoad);
        win.webContents.send('load-url', urlToLoad);
      });
    }
  });
}

// 初始化应用程序
app.whenReady().then(async () => {
  await ensureBookmarksFile()

  // 检查启动参数中的 URL
  const url = process.argv[process.argv.length - 1];
  console.log('Initial URL:', url);

  // 设置 Dock 菜单
  if (process.platform === 'darwin') {
    app.dock.show();
    const dockMenu = Menu.buildFromTemplate([
      {
        label: '新建窗口',
        click() {
          if (app.isReady()) {
            createWindow();
          }
        }
      }
    ]);
    app.dock.setMenu(dockMenu);
  }

  // 如果有待处理的 URL 或启动参数中的 URL/文件，创建新窗口并加载
  let shouldCreateWindow = false;
  let urlToLoad = null;

  if (pendingUrl) {
    urlToLoad = pendingUrl;
    shouldCreateWindow = true;
  } else if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
    urlToLoad = url;
    shouldCreateWindow = true;
  } else if (url && (url.toLowerCase().endsWith('.html') || url.toLowerCase().endsWith('.htm'))) {
    // 处理HTML文件路径
    urlToLoad = `file://${url}`;
    shouldCreateWindow = true;
    console.log('Converting startup file path to URL:', urlToLoad);
  }

  if (shouldCreateWindow) {
    const win = createWindow();
    win.webContents.once('did-finish-load', () => {
      console.log('Loading URL in new window:', urlToLoad);
      win.webContents.send('load-url', urlToLoad);
      pendingUrl = null;
    });
  } else if (BrowserWindow.getAllWindows().length === 0) {
    // 只有在没有其他窗口时才创建空白窗口
    createWindow();
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// Add IPC handler to get homepage path
ipcMain.handle('get-homepage-path', () => {
  return app.isPackaged
    ? path.join(process.resourcesPath, 'homepage.html')
    : path.join(__dirname, 'homepage.html')
})

// 处理获取窗口位置的请求（全局只注册一次）
ipcMain.handle('get-window-position', (event) => {
  const win = BrowserWindow.fromWebContents(event.sender);
  if (win) {
    const position = win.getPosition();
    return {
      x: position[0],
      y: position[1]
    };
  }
  return { x: 0, y: 0 };
});

function createWindow() {
  // 计算新窗口的位置
  const offset = 30; // 每个新窗口的偏移量
  let x = 0;
  let y = 0;

  // 获取主屏幕的尺寸
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

  // 获取所有已存在的窗口
  const existingWindows = BrowserWindow.getAllWindows();

  if (existingWindows.length === 0) {
    // 第一个窗口，计算屏幕中心位置
    x = Math.round((screenWidth - 1200) / 2);
    y = Math.round((screenHeight - 800) / 2);
  } else {
    // 计算新窗口的位置，基于窗口数量来确定偏移量
    const windowCount = existingWindows.length;
    x = Math.round((screenWidth - 1200) / 2) + (offset * windowCount);
    y = Math.round((screenHeight - 800) / 2) + (offset * windowCount);

    // 如果新位置会导致窗口超出屏幕，重置到起始位置并调整偏移
    if (x + 1200 > screenWidth || y + 800 > screenHeight) {
      // 重置到左上角附近，但留出一定边距
      x = offset;
      y = offset;

      // 如果还有空间，根据已开启窗口数量稍微偏移
      const maxOffsetX = screenWidth - 1200 - offset;
      const maxOffsetY = screenHeight - 800 - offset;
      const newOffsetX = (windowCount % 3) * offset; // 最多错开3个位置
      const newOffsetY = (windowCount % 3) * offset;

      // 确保不会超出屏幕
      x = Math.min(x + newOffsetX, maxOffsetX);
      y = Math.min(y + newOffsetY, maxOffsetY);
    }
  }

  // 创建一个初始透明的窗口
  const win = new BrowserWindow({
    width: 1200,
    height: 800,
    x: x,
    y: y,
    frame: false,
    titleBarStyle: 'hidden',
    vibrancy: 'under-window',
    visualEffectState: 'active',
    opacity: 0.0, // 初始设置为完全透明
    show: false,  // 初始不显示
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webviewTag: true,
      webSecurity: false,
      allowRunningInsecureContent: true,
      nativeWindowOpen: true,
      javascript: true,
      experimentalFeatures: true,
      enableRemoteModule: false
    },
    backgroundColor: '#fff'
  })

  // 直接标记这是一个主窗口
  win.isMainWindow = true;
  mainWindowIds.add(win.id);

  // 当窗口关闭时，清理标记
  win.on('closed', () => {
    mainWindowIds.delete(win.id);
  });

  // 窗口准备好后再显示，并添加动画效果
  win.once('ready-to-show', () => {
    win.show();
    // 使用动画效果逐渐显示窗口
    let opacity = 0.0;
    const targetOpacity = 1.0;
    const step = 0.05;
    const interval = 10; // 每10毫秒更新一次

    const fadeIn = setInterval(() => {
      if (opacity < targetOpacity) {
        opacity += step;
        win.setOpacity(opacity);
      } else {
        clearInterval(fadeIn);
        // 读取并应用保存的透明度设置
        fs.readFile(bookmarksPath, 'utf8')
          .then(data => {
            const json = JSON.parse(data);
            if (json.settings && json.settings.opacity) {
              win.setOpacity(json.settings.opacity);
            }
          })
          .catch(error => {
            console.error('Error loading opacity setting:', error);
          });
      }
    }, interval);
  });

  // Initialize window state
  win.windowState = { isTrafficLightVisible: false };

  // Hide traffic lights by default
  if (process.platform === 'darwin') {
    win.setWindowButtonVisibility(false);
  }

  // 读取并应用保存的设置
  fs.readFile(bookmarksPath, 'utf8')
    .then(data => {
      let json = { settings: {} };
      if (data.trim()) {
        try {
          json = JSON.parse(data);
        } catch (parseError) {
          console.error('Error parsing settings file:', parseError);
        }
      }
      
      if (json.settings) {
        if (json.settings.opacity) {
          win.setOpacity(json.settings.opacity);
        }
        if (json.settings.showTitlebar === true) {
          win.windowState = { isTrafficLightVisible: true };
          win.setWindowButtonVisibility(true);
          // 等待页面加载完成后再发送toggle-titlebar消息
          win.webContents.on('did-finish-load', () => {
            win.webContents.send('toggle-titlebar', true);
          });
        } else {
          // 默认隐藏标题栏，确保发送隐藏消息
          win.webContents.on('did-finish-load', () => {
            win.webContents.send('toggle-titlebar', false);
          });
        }
      } else {
        // 如果没有设置，默认隐藏标题栏
        win.webContents.on('did-finish-load', () => {
          win.webContents.send('toggle-titlebar', false);
        });
      }
    })
    .catch(error => {
      console.error('Error loading settings:', error);
      // 如果出错，默认隐藏标题栏
      win.webContents.on('did-finish-load', () => {
        win.webContents.send('toggle-titlebar', false);
      });
    });

  win.webContents.setWindowOpenHandler((details) => {
    // Check if it's a window.open() call or has JavaScript features
    const isJavaScriptPopup = details.disposition === 'new-window' &&
      (details.features || details.frameName || details.referrer.policy !== '');

    if (!isJavaScriptPopup) {
      // If it's not a JavaScript popup, open it in the main process
      const newWin = createWindow(); // 这会被标记为主窗口
      newWin.webContents.once('did-finish-load', () => {
        // 先显示 dragarea，然后加载 URL
        newWin.windowState.isTrafficLightVisible = true;
        newWin.setWindowButtonVisibility(true);
        newWin.webContents.send('toggle-titlebar', true);

        // 短暂延迟后加载 URL，确保 dragarea 已显示
        setTimeout(() => {
          newWin.webContents.send('load-url', details.url);
        }, 50);
      });
      return { action: 'deny' };
    }

    // 注意：这里不需要设置全局标记
    // 因为我们将通过排除法（不在主窗口集合中）来识别弹出窗口

    // Allow JavaScript popups to open normally
    return {
      action: 'allow',
      overrideBrowserWindowOptions: {
        frame: false,
        titleBarStyle: 'hidden',
        vibrancy: 'under-window',
        visualEffectState: 'active',
        backgroundColor: '#fff',
        webPreferences: {
          nodeIntegration: true,
          contextIsolation: false,
          webviewTag: true,
          webSecurity: false,
          allowRunningInsecureContent: true,
          nativeWindowOpen: true,
          javascript: true
        }
      }
    };
  });

  // 监听窗口移动
  win.on('move', () => {
    const position = win.getPosition();
    win.webContents.send('window-moved', {
      x: position[0],
      y: position[1]
    });
  });

  // 添加窗口级的 ESC 键监听
  win.webContents.on('before-input-event', (event, input) => {
    if (input.type === 'keyDown' && input.key === 'Escape' && !event.defaultPrevented) {
      console.log('Window-level ESC key pressed');
      // 调用 ESC 键处理函数
      handleEscapeKey();
      // 阻止事件继续传播
      event.preventDefault();
    }
  });

  // 修改主session的下载处理，添加对特定网站的特殊处理
  session.defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
    // 检查是否是需要特殊处理的网站
    if (details.url.includes('vscode.dev') || details.url.includes('marketplace') || details.url.includes('vscode-cdn.net')) {
      // 对于VSCode相关网站，使用最新的Chrome用户代理以支持tunnel功能
      callback({
        requestHeaders: {
          ...details.requestHeaders,
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Accept-Language': 'en-US,en;q=0.9',
          'Sec-Fetch-Site': 'none',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-User': '?1',
          'Sec-Fetch-Dest': 'document',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Ch-Ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
          'Sec-Ch-Ua-Mobile': '?0',
          'Sec-Ch-Ua-Platform': '"macOS"'
        }
      });
    } else if (details.url.includes('google.com') || details.url.includes('accounts.google')) {
      // 使用最新的Chrome用户代理来避免Google的浏览器不支持提示
      callback({
        requestHeaders: {
          ...details.requestHeaders,
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Accept-Language': 'en-US,en;q=0.9',
          'Sec-Fetch-Site': 'none',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-User': '?1',
          'Sec-Fetch-Dest': 'document',
          'Upgrade-Insecure-Requests': '1'
        }
      });
    } else {
      // 对其他网站使用默认用户代理
      callback({
        requestHeaders: {
          ...details.requestHeaders,
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Accept-Language': 'en-US,en;q=0.9',
          'Sec-Fetch-Site': 'same-origin',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-User': '?1',
          'Sec-Fetch-Dest': 'document',
          'Upgrade-Insecure-Requests': '1',
          'Origin': details.url
        }
      });
    }
  });

  // Configure CSP to allow injected scripts execution
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    const responseHeaders = { ...details.responseHeaders };

    // Convert header names to lowercase for consistent handling
    const normalizedHeaders = {};
    Object.keys(responseHeaders).forEach(key => {
      normalizedHeaders[key.toLowerCase()] = responseHeaders[key];
    });

    // Only remove CSP headers
    Object.keys(normalizedHeaders).forEach(key => {
      if (key.toLowerCase().includes('content-security-policy')) {
        delete normalizedHeaders[key];
      }
    });

    // Remove X-Frame-Options header
    if (normalizedHeaders['x-frame-options']) {
      delete normalizedHeaders['x-frame-options'];
    }
    if (normalizedHeaders['X-Frame-Options']) {
      delete normalizedHeaders['X-Frame-Options'];
    }

    callback({ responseHeaders: normalizedHeaders });
  });

  // Also set up key handlers for any webview that gets attached
  win.webContents.on('did-attach-webview', (event, webContents) => {
    // Configure webview settings
    webContents.setWindowOpenHandler((details) => {
      console.log('Window open handler called with URL:', details.url);

      // 检查是否是JavaScript弹出窗口
      const isJavaScriptPopup = details.disposition === 'new-window' &&
        (details.features || details.frameName || details.referrer.policy !== '');

      // 检查是否可能是下载请求
      const isPossibleDownload = details.url.includes('download=') ||
      details.url.includes('export=') ||
      details.url.includes('file=');

      // 如果可能是下载请求，直接允许以原始方式打开以保留referrer
      if (isPossibleDownload || details.disposition === 'foreground-tab') {
        console.log('Letting browser handle potential download URL in webview:', details.url);
        return { action: 'allow' };
      }

      if (!isJavaScriptPopup) {
        // 如果不是JavaScript弹出窗口，在主进程中创建新窗口
        const newWin = createWindow();
        newWin.webContents.once('did-finish-load', () => {
          newWin.webContents.send('load-url', details.url);
        });
        return { action: 'deny' };
      }

      // 如果是JavaScript弹出窗口，允许正常打开
      return { action: 'allow' };
    });

    // 添加 ESC 键监听到 webview
    webContents.on('before-input-event', (event, input) => {
      if (input.type === 'keyDown' && input.key === 'Escape' && !event.defaultPrevented) {
        console.log('Webview ESC key pressed');
        // 停止当前 webview 的加载
        if (!webContents.isDestroyed()) {
          webContents.stop();
        }

        // 获取对应的窗口
        const win = BrowserWindow.fromWebContents(webContents.hostWebContents);
        if (win && !win.isDestroyed() && win.webContents && !win.webContents.isDestroyed()) {
          win.webContents.send('handle-escape');
        }

        // 阻止事件继续传播
        event.preventDefault();
      }
    });

    // 添加 beforeunload 事件处理
    webContents.on('will-prevent-unload', (event) => {
      console.log('Preventing unload prevention');
      event.preventDefault();
    });

    // Configure webview session with special handling for sites that block certain user agents
    webContents.session.webRequest.onBeforeSendHeaders((details, callback) => {
      // 检查是否是需要特殊处理的网站
      if (details.url.includes('vscode.dev') || details.url.includes('marketplace') || details.url.includes('vscode-cdn.net')) {
        // 对于VSCode相关网站，使用最新的Chrome用户代理以支持tunnel功能
        callback({
          requestHeaders: {
            ...details.requestHeaders,
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Ch-Ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"'
          }
        });
      } else if (details.url.includes('google.com') || details.url.includes('accounts.google')) {
        // 使用最新的Chrome用户代理来避免Google的浏览器不支持提示
        callback({
          requestHeaders: {
            ...details.requestHeaders,
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
            'Upgrade-Insecure-Requests': '1'
          }
        });
      } else {
        // 对其他网站使用默认处理
        callback({ requestHeaders: details.requestHeaders });
      }
    });

    // Configure webview CSP
    // webContents.session.webRequest.onHeadersReceived((details, callback) => {
    //   const responseHeaders = { ...details.responseHeaders };

    //   // Convert header names to lowercase for consistent handling
    //   const normalizedHeaders = {};
    //   Object.keys(responseHeaders).forEach(key => {
    //     normalizedHeaders[key.toLowerCase()] = responseHeaders[key];
    //   });

    //   // Only remove CSP headers to allow script injection
    //   delete normalizedHeaders['content-security-policy'];
    //   delete normalizedHeaders['content-security-policy-report-only'];
    //   delete normalizedHeaders['x-frame-options'];

    //   callback({ responseHeaders: normalizedHeaders });
    // });

    // Add key event handlers for webview
    webContents.on('before-input-event', (event, input) => {
      // Check for Command key
      if (input.key === 'Meta') {
        win.webContents.send('command-key-change', input.type === 'keyDown');
      }
      // Check for Shift key
      if (input.key === 'Shift') {
        win.webContents.send('shift-key-change', input.type === 'keyDown');
      }
    });

    // Reset key states when webview loses focus
    // webContents.on('blur', () => {
    //   win.webContents.send('reset-key-states');
    // });

    // 生成唯一的文件名
    async function generateUniqueFilePath(basePath, filename) {
      const ext = path.extname(filename);
      const nameWithoutExt = path.basename(filename, ext);
      let filePath = path.join(basePath, filename);
      let counter = 1;

      while (true) {
        try {
          await fs.access(filePath);
          // 文件存在，尝试新的名字
          filePath = path.join(basePath, `${nameWithoutExt}(${counter})${ext}`);
          counter++;
        } catch {
          // 文件不存在，可以使用这个路径
          return filePath;
        }
      }
    }

    // 添加一个通用的下载处理函数
    async function handleDownload(event, item, webContents) {
      // 获取基本信息
      const url = item.getURL();
      const filename = item.getFilename();
      const downloadPath = app.getPath('downloads');

      // 创建临时文件名 (使用时间戳和随机数确保唯一性)
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substring(7);
      const tempFilename = `temp_${timestamp}_${randomStr}_${filename}`;
      const tempFilePath = path.join(downloadPath, "temp-" + filename);

      // 创建唯一的下载标识符
      const downloadKey = `${url}_${filename}`;
      const downloadId = timestamp.toString();

      // 立即设置保存路径为临时文件
      item.setSavePath(tempFilePath);

      // 检查是否正在处理这个下载
      if (processingDownloads.has(downloadKey)) {
        console.log('下载正在处理中，取消重复下载:', filename);
        //item.cancel();
        return;
      }

      // 标记正在处理这个下载
      processingDownloads.add(downloadKey);

      // 记录下载来源窗口
      const sourceWindow = BrowserWindow.fromWebContents(webContents);
      if (sourceWindow) {
        downloadSourceWindows.set(downloadId, sourceWindow);
        console.log(`记录下载来源窗口: ${downloadId}, URL: ${url}`);
      }

      try {
        console.log('开始新下载:', filename);

        // 创建下载管理器窗口（如果不存在）
        if (!downloadWindow) {
          createDownloadWindow();
        }

        // 创建下载项对象
        const downloadItemWrapper = {
          id: downloadId,
          filename: filename,
          actualFilename: tempFilename,
          url: url,
          totalBytes: item.getTotalBytes(),
          receivedBytes: 0,
          state: 'progressing',
          lastReceivedBytes: 0,
          lastUpdateTime: Date.now(),
          noProgressCount: 0,
          pause: () => item.pause(),
          resume: () => item.resume(),
          cancel: () => item.cancel(),
          getSavePath: () => tempFilePath,
          getFilename: () => filename,
          getURL: () => url,
          getTotalBytes: () => item.getTotalBytes(),
          getReceivedBytes: () => item.getReceivedBytes(),
          getState: () => item.getState(),
          isPaused: () => item.isPaused()
        };

        // 将下载项添加到downloads Map中
        downloads.set(downloadId, downloadItemWrapper);

        // 发送下载开始事件到下载管理器窗口
        if (downloadWindow) {
          downloadWindow.webContents.send('download-started', {
            id: downloadId,
            filename: filename,
            url: url,
            totalBytes: downloadItemWrapper.getTotalBytes()
          });
        }

        // 更新判断弹出窗口的代码部分
        // 短暂延迟后关闭源窗口，确保下载已经开始
        setTimeout(() => {
          const sourceWin = downloadSourceWindows.get(downloadId);
          if (sourceWin && !sourceWin.isDestroyed()) {
            // 使用新的判断方法
            const isPopup = isPopupWindow(sourceWin);

            if (isPopup) {
              console.log(`关闭下载来源弹出窗口: ${downloadId}, 窗口ID: ${sourceWin.id}, 不在主窗口列表中`);
              sourceWin.close();
            } else {
              console.log(`下载来源是主窗口，不关闭: ${downloadId}, 窗口ID: ${sourceWin.id}`);
            }
          }
        }, 300);

        // 创建一个检查下载进度的定时器
        let progressCheckInterval;
        const checkDownloadProgress = async () => {
          try {
            const currentBytes = item.getReceivedBytes();
            const currentTime = Date.now();

            // 检查是否有新的数据接收
            if (currentBytes === downloadItemWrapper.lastReceivedBytes) {
              downloadItemWrapper.noProgressCount++;

              // 如果连续30秒没有进度变化，但文件大小仍在增长
              if (downloadItemWrapper.noProgressCount >= 30) {
                const stats = await fs.stat(tempFilePath);
                const fileSize = stats.size;

                // 如果文件大小确实没有变化，可能下载已经完成
                if (fileSize === currentBytes) {
                  clearInterval(progressCheckInterval);
                  if (downloadWindow && !downloadWindow.isDestroyed()) {
                    downloadWindow.webContents.send('download-state', {
                      id: downloadId,
                      state: '下载完成',
                      filename: filename
                    });
                  }
                  downloads.delete(downloadId);
                  processingDownloads.delete(downloadKey);
                }
              }
            } else {
              // 重置计数器
              downloadItemWrapper.noProgressCount = 0;
              downloadItemWrapper.lastReceivedBytes = currentBytes;
              downloadItemWrapper.lastUpdateTime = currentTime;
            }
          } catch (error) {
            console.error('检查下载进度时出错:', error);
          }
        };

        // 每秒检查一次下载进度
        progressCheckInterval = setInterval(checkDownloadProgress, 1000);

        // 监听下载进度
        item.on('updated', (event, state) => {
          try {
            if (state === 'progressing') {
              const receivedBytes = item.getReceivedBytes();
              const totalBytes = item.getTotalBytes();
              const progress = {
                id: downloadId,
                percent: totalBytes ? (receivedBytes * 100 / totalBytes).toFixed(2) : 0,
                transferred: receivedBytes,
                total: totalBytes || 0,
                state: 'progressing'
              };
              if (downloadWindow && !downloadWindow.isDestroyed()) {
                downloadWindow.webContents.send('download-progress', progress);
              }
            }
          } catch (error) {
            console.error('下载进度更新出错:', error);
          }
        });

        // 监听下载完成
        item.once('done', async (event, state) => {
          try {
            clearInterval(progressCheckInterval);

            // 清理下载源窗口映射
            downloadSourceWindows.delete(downloadId);

            if (state === 'completed') {
              // 再次检查文件大小是否还在变化
              const initialSize = (await fs.stat(tempFilePath)).size;

              // 等待3秒后再次检查文件大小
              await new Promise(resolve => setTimeout(resolve, 3000));

              const finalSize = (await fs.stat(tempFilePath)).size;

              // 如果文件大小不再变化，则认为下载真正完成
              if (finalSize === initialSize) {
                // 生成最终的文件名
                const finalFilePath = await generateUniqueFilePath(downloadPath, filename);

                // 重命名临时文件为最终文件名
                await fs.rename(tempFilePath, finalFilePath);

                completedDownloadPaths.set(downloadId, finalFilePath);
                if (downloadWindow && !downloadWindow.isDestroyed()) {
                  downloadWindow.webContents.send('download-state', {
                    id: downloadId,
                    state: '下载完成',
                    filename: path.basename(finalFilePath)
                  });
                }
              } else {
                // 如果文件还在增长，继续监控
                const newProgressCheckInterval = setInterval(async () => {
                  try {
                    const currentSize = (await fs.stat(tempFilePath)).size;
                    if (currentSize === finalSize) {
                      clearInterval(newProgressCheckInterval);

                      // 生成最终的文件名
                      const finalFilePath = await generateUniqueFilePath(downloadPath, filename);

                      // 重命名临时文件为最终文件名
                      await fs.rename(tempFilePath, finalFilePath);

                      completedDownloadPaths.set(downloadId, finalFilePath);
                      if (downloadWindow && !downloadWindow.isDestroyed()) {
                        downloadWindow.webContents.send('download-state', {
                          id: downloadId,
                          state: '下载完成',
                          filename: path.basename(finalFilePath)
                        });
                      }
                    }
                    finalSize = currentSize;
                  } catch (error) {
                    console.error('检查文件大小时出错:', error);
                    clearInterval(newProgressCheckInterval);
                  }
                }, 1000);
              }
            } else {
              // 如果下载失败，删除临时文件
              try {
                await fs.unlink(tempFilePath);
              } catch (unlinkError) {
                console.error('删除临时文件失败:', unlinkError);
              }

              if (downloadWindow && !downloadWindow.isDestroyed()) {
                downloadWindow.webContents.send('download-state', {
                  id: downloadId,
                  state: '下载失败',
                  filename: filename
                });
              }
            }
          } catch (error) {
            console.error('下载完成处理出错:', error);
            // 尝试删除临时文件
            try {
              await fs.unlink(tempFilePath);
            } catch (unlinkError) {
              console.error('删除临时文件失败:', unlinkError);
            }

            if (downloadWindow && !downloadWindow.isDestroyed()) {
              downloadWindow.webContents.send('download-state', {
                id: downloadId,
                state: '下载出错',
                filename: filename
              });
            }
          } finally {
            downloads.delete(downloadId);
            processingDownloads.delete(downloadKey);
            downloadSourceWindows.delete(downloadId);
          }
        });

      } catch (error) {
        console.error('下载处理出错:', error);
        // 尝试删除临时文件
        try {
          await fs.unlink(tempFilePath);
        } catch (unlinkError) {
          console.error('删除临时文件失败:', unlinkError);
        }

        if (downloadWindow && !downloadWindow.isDestroyed()) {
          downloadWindow.webContents.send('download-state', {
            id: downloadId,
            state: '下载出错',
            filename: filename
          });
        }
        downloads.delete(downloadId);
        processingDownloads.delete(downloadKey);
        downloadSourceWindows.delete(downloadId);
      }
    }

    // 在主进程中设置下载处理
    //session.defaultSession.on('will-download', handleDownload);

    // 在webview的session中设置下载处理
    if (!registeredDownloadHandlers.has(webContents.id)) {
        webContents.session.on('will-download', handleDownload);
        registeredDownloadHandlers.add(webContents.id);

        // 当webContents被销毁时，移除记录
        webContents.on('destroyed', () => {
            registeredDownloadHandlers.delete(webContents.id);
        });
    }

    // Modify webview security settings
    webContents.session.webRequest.onHeadersReceived((details, callback) => {
      const responseHeaders = { ...details.responseHeaders };

      // Only remove CSP headers
      Object.keys(responseHeaders).forEach(key => {
        if (key.toLowerCase().includes('content-security-policy')) {
          delete responseHeaders[key];
        }
      });

      // Remove X-Frame-Options header
      Object.keys(responseHeaders).forEach(key => {
        if (key.toLowerCase() === 'x-frame-options') {
          delete responseHeaders[key];
        }
      });

      callback({ responseHeaders });
    });

    // Initialize DOM modifications with enhanced TrustedTypes handling
    webContents.executeJavaScript(`
      // Initialize DOM modifications only once
      if (!window.__domModificationsInitialized) {
        window.__domModificationsInitialized = true;

        try {
          // Handle TrustedTypes more comprehensively
          if (window.trustedTypes && window.trustedTypes.createPolicy) {
            try {
              // Create a permissive policy that will be used as fallback
              const policy = window.trustedTypes.createPolicy('default', {
                createHTML: (s) => s,
                createScript: (s) => s,
                createScriptURL: (s) => s
              });
              console.log('Created permissive TrustedTypes policy');
            } catch (policyError) {
              console.log('Could not create default policy, trying to handle existing policies');

              // If we can't create a default policy, try to patch the existing ones
              const originalCreatePolicy = window.trustedTypes.createPolicy;
              window.trustedTypes.createPolicy = function(policyName, policyOptions) {
                try {
                  return originalCreatePolicy.call(window.trustedTypes, policyName, {
                    createHTML: (s) => s,
                    createScript: (s) => s,
                    createScriptURL: (s) => s,
                    ...policyOptions
                  });
                } catch (e) {
                  console.warn('Failed to create policy:', policyName, e);
                  // Return a mock policy if creation fails
                  return {
                    createHTML: (s) => s,
                    createScript: (s) => s,
                    createScriptURL: (s) => s,
                    ...(policyOptions || {})
                  };
                }
              };
            }
          } else {
            // If trustedTypes is not available, create a mock implementation
            window.trustedTypes = {
              createPolicy: (name, rules) => rules,
              defaultPolicy: {
                createHTML: (s) => s,
                createScript: (s) => s,
                createScriptURL: (s) => s
              }
            };
          }

          // Add helper functions
          window.createTrustedHTML = (html) => html;

          window.setInnerHTML = (element, html) => {
            try {
              element.innerHTML = html;
            } catch (e) {
              console.warn('Failed to set innerHTML:', e);
            }
          };

          window.insertAdjacentHTML = (element, position, html) => {
            try {
              element.insertAdjacentHTML(position, html);
            } catch (e) {
              console.warn('Failed to insert adjacent HTML:', e);
            }
          };

          // Override Element prototype methods
          const elementProto = Element.prototype;

          // Override innerHTML
          const originalInnerHTML = Object.getOwnPropertyDescriptor(elementProto, 'innerHTML');
          Object.defineProperty(elementProto, 'innerHTML', {
            configurable: true,
            enumerable: true,
            get: originalInnerHTML.get,
            set(value) {
              try {
                return originalInnerHTML.set.call(this, value);
              } catch (e) {
                console.warn('Failed to set innerHTML, retrying without security checks');
                const div = document.createElement('div');
                div.innerHTML = value;
                while (this.firstChild) {
                  this.removeChild(this.firstChild);
                }
                while (div.firstChild) {
                  this.appendChild(div.firstChild);
                }
              }
            }
          });

          console.log('Enhanced DOM modifications initialized successfully');
        } catch (e) {
          console.error('Failed to initialize DOM modifications:', e);
        }
      }
    `, true).catch(e => console.error('Failed to execute DOM modifications:', e));

    // Inject scripts for each webview with proper timing and scope isolation
    webContents.on('did-finish-load', async () => {
      try {
        console.log('Webview finished loading, checking scripts status...');

        // Check if scripts are already loaded
        const scriptsStatus = await webContents.executeJavaScript(`
          (function() {
            if (!window._loadedScripts) window._loadedScripts = new Set();
            return {
              loadedScripts: Array.from(window._loadedScripts),
              isInitialized: window._scriptsInitialized || false
            };
          })()
        `, true);

        if (scriptsStatus.isInitialized) {
          console.log('Scripts already initialized, skipping...');
          return;
        }

        const scripts = await loadUserScripts();
        for (const script of scripts) {
          console.log(`Checking script: ${script.name}`);
          try {
            // Check if this specific script is already loaded
            if (scriptsStatus.loadedScripts.includes(script.name)) {
              console.log(`Script ${script.name} already loaded, skipping...`);
              continue;
            }

            const wrappedScript = `
              (function() {
                try {
                  if (!window._loadedScripts) window._loadedScripts = new Set();
                  if (!window._loadedScripts.has('${script.name}')) {
                    console.log('[Script Start] ${script.name}');
                    const unsafeWindow = window;
                    const GM_info = {
                      script: {
                        name: '${script.name}',
                        namespace: 'electron-userscript'
                      },
                      version: '1.0'
                    };
                    ${script.content}
                    window._loadedScripts.add('${script.name}');
                    console.log('[Script End] ${script.name}');
                  }
                } catch (error) {
                  console.error('[Script Error] ${script.name}:', error);
                }
              })();
            `;
            await webContents.executeJavaScript(wrappedScript, true);
          } catch (err) {
            console.error(`Error executing script ${script.name}:`, err);
          }
        }

        // Mark all scripts as initialized
        await webContents.executeJavaScript('window._scriptsInitialized = true;', true);
      } catch (error) {
        console.error('Error loading or executing user scripts:', error);
      }
    });

    // Enable more detailed logging for the webview
    webContents.on('console-message', (event, level, message, line, sourceId) => {
      console.log(`WebView Console (${level}): ${message}`);
      if (sourceId) console.log(`Source: ${sourceId}:${line}`);
    });

    // Inject scripts for each webview with proper timing
    webContents.on('did-start-loading', async () => {
      console.log('Webview started loading...');
    });

    // Also try to inject on dom-ready with error handling
    webContents.on('dom-ready', async () => {
      try {
        console.log('DOM is ready');
        // Remove script injection from dom-ready event
      } catch (error) {
        console.error('Error in dom-ready event:', error);
      }
    });
  })

  // Configure menu and shortcuts
  const template = [
    {
      label: 'App',
      submenu: [
        {
          label: 'New Window',
          accelerator: 'CmdOrCtrl+N',
          click: () => createWindow()
        },
        {
          label: '下载管理器',
          accelerator: 'CmdOrCtrl+O',
          click: () => createDownloadWindow()
        },
        {
          label: '添加书签',
          accelerator: 'CmdOrCtrl+B',
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              browserWindow.webContents.send('save-bookmark');
              showToast();
            }
          }
        },
        {
          label: '打开书签',
          accelerator: 'Alt+B',
          click: () => createBookmarksWindow()
        },
        {
          label: '强制刷新',
          accelerator: 'CmdOrCtrl+T',
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              browserWindow.webContents.send('get-webview-info');
            }
          }
        },
        {
          label: '页面内搜索',
          accelerator: 'CmdOrCtrl+F',
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              browserWindow.webContents.send('show-search-modal');
            }
          }
        },
        {
          label: 'Close Window',
          accelerator: 'CmdOrCtrl+W',
          click: (menuItem, browserWindow) => browserWindow && browserWindow.close()
        },
        {
          label: 'Quit',
          accelerator: 'CmdOrCtrl+Q',
          click: () => app.quit()
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { label: 'Undo', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'Redo', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'Cut', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'Copy', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'Paste', accelerator: 'CmdOrCtrl+V', role: 'paste' },
        { label: 'Select All', accelerator: 'CmdOrCtrl+A', role: 'selectAll' }
      ]
    },
    {
      label: 'Navigation',
      submenu: [
        {
          label: 'Toggle Traffic Lights',
          accelerator: 'CmdOrCtrl+E',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              toggleTrafficLights(browserWindow);
            }
          }
        },
        {
          label: 'Show Current URL',
          accelerator: 'CmdOrCtrl+,',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.webContents.send('show-current-url')
            }
          }
        },
        {
          label: 'Minimize Window',
          accelerator: 'CmdOrCtrl+M',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.minimize()
            }
          }
        },
        {
          label: 'Enter URL',
          accelerator: 'CmdOrCtrl+I',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.webContents.send('load-homepage')
            }
          }
        },
        {
          label: 'Set Opacity',
          accelerator: 'CmdOrCtrl+;',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.webContents.send('show-opacity-modal')
            }
          }
        },
        {
          label: 'Zoom In',
          accelerator: 'CmdOrCtrl+=',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.webContents.send('zoom-in')
            }
          }
        },
        {
          label: 'Zoom Out',
          accelerator: 'CmdOrCtrl+-',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.webContents.send('zoom-out')
            }
          }
        },
        {
          label: 'Reset Zoom',
          accelerator: 'CmdOrCtrl+0',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.webContents.send('zoom-reset')
            }
          }
        },
        {
          label: 'Back',
          accelerator: 'CmdOrCtrl+J',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.webContents.send('go-back')
            }
          }
        },
        {
          label: 'Back (Alt)',
          accelerator: 'Alt+S',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.webContents.send('go-back')
            }
          }
        },
        {
          label: 'Forward',
          accelerator: 'CmdOrCtrl+L',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.webContents.send('go-forward')
            }
          }
        },
        {
          label: 'Forward (Alt)',
          accelerator: 'Alt+D',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.webContents.send('go-forward')
            }
          }
        },
        {
          label: 'Refresh',
          accelerator: 'CmdOrCtrl+R',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.webContents.send('refresh')
            }
          }
        },
        {
          label: 'Toggle Developer Tools',
          accelerator: 'CmdOrCtrl+D',
          click: (menuItem, browserWindow) => {
            if (browserWindow && !browserWindow.isDestroyed()) {
              browserWindow.webContents.send('toggle-devtools')
            }
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)

  win.loadFile('index.html')

  // Show URL input box after window loads
  win.webContents.on('did-finish-load', () => {
    win.webContents.send('show-url-modal')
    // Load homepage
    win.webContents.send('load-homepage')
  })

  return win
}

// Centralized function to handle traffic lights toggle
async function toggleTrafficLights(win) {
  if (win && !win.isDestroyed()) {
    win.windowState.isTrafficLightVisible = !win.windowState.isTrafficLightVisible;
    win.setWindowButtonVisibility(win.windowState.isTrafficLightVisible);

    // 发送状态到渲染进程，让其处理dragArea
    win.webContents.send('toggle-titlebar', win.windowState.isTrafficLightVisible);

    // 读取当前设置并更新
    try {
      let json = { settings: {}, bookmarks: [] };
      try {
        const data = await fs.readFile(bookmarksPath, 'utf8');
        if (data.trim()) {  // 只在文件不为空时解析
          json = JSON.parse(data);
        }
      } catch (readError) {
        // 如果文件不存在或为空，使用默认值
        console.log('Creating new settings file');
      }

      if (!json.settings) {
        json.settings = {};
      }
      json.settings.showTitlebar = win.windowState.isTrafficLightVisible;
      await fs.writeFile(bookmarksPath, JSON.stringify(json, null, 2), 'utf8');
    } catch (error) {
      console.error('Error saving titlebar state:', error);
    }
  }
}

// Add IPC handler for toggling traffic lights from renderer
ipcMain.on('toggle-traffic-lights', (event) => {
  const win = BrowserWindow.fromWebContents(event.sender);
  toggleTrafficLights(win);
});

ipcMain.on('set-opacity', async (event, opacity) => {
  const win = BrowserWindow.fromWebContents(event.sender)
  const opacityValue = parseFloat(opacity)

  if (opacityValue >= 0.1 && opacityValue <= 1.0) {
    // Apply opacity to all windows, not just the originating window
    BrowserWindow.getAllWindows().forEach(window => {
      if (!window.isDestroyed()) {
        window.setOpacity(opacityValue);
      }
    });

    // 保存透明度设置
    try {
      let json = { bookmarks: [], settings: {} };
      try {
        const data = await fs.readFile(bookmarksPath, 'utf8');
        if (data.trim()) {  // 只在文件不为空时解析
          json = JSON.parse(data);
        }
      } catch (readError) {
        // 如果文件不存在或为空，使用默认值
        console.log('Creating new settings file for opacity');
      }

      if (!json.settings) {
        json.settings = {};
      }
      if (!json.bookmarks) {
        json.bookmarks = [];
      }
      json.settings.opacity = opacityValue;
      await fs.writeFile(bookmarksPath, JSON.stringify(json, null, 2), 'utf8');
    } catch (error) {
      console.error('Error saving opacity setting:', error);
    }
  }
})

ipcMain.on('update-dock-title', (event, title) => {
  const win = BrowserWindow.fromWebContents(event.sender)
  if (process.platform === 'darwin' && win && !win.isDestroyed()) {
    win.setTitle(title)  // Set window title only
  }
})

// Extract user script loading logic as independent function
async function loadUserScripts() {
  const userScriptsPath = app.isPackaged
    ? path.join(process.resourcesPath, 'userscripts')
    : path.join(app.getAppPath(), 'userscripts');

  try {
    // Ensure directory exists
    try {
      await fs.access(userScriptsPath);
    } catch {
      await fs.mkdir(userScriptsPath, { recursive: true });
    }

    // Read all .js files in the directory
    const files = await fs.readdir(userScriptsPath);
    const scripts = [];

    for (const file of files) {
      if (file.endsWith('.js') || file.endsWith('.user.js')) {
        const filePath = path.join(userScriptsPath, file);
        const content = await fs.readFile(filePath, 'utf-8');
        scripts.push({
          name: file,
          content: content
        });
      }
    }

    return scripts;
  } catch (error) {
    console.error('加载用户脚本时出错:', error);
    return [];
  }
}

// Modify IPC handler to use common loading function
ipcMain.handle('load-user-scripts', loadUserScripts);

// 处理新窗口创建请求
ipcMain.on('create-new-window', (event, url) => {
  console.log('Creating new window for URL:', url);
  const win = createWindow();

  // 存储要加载的URL
  win.urlToLoad = url;

  // 监听渲染进程准备就绪的消息
  ipcMain.once('window-ready', (readyEvent) => {
    // 确保消息来自正确的窗口
    if (readyEvent.sender === win.webContents) {
      console.log('Window signaled ready, sending URL:', url);

      // 先显示 dragarea，然后加载 URL
      win.windowState.isTrafficLightVisible = true;
      win.setWindowButtonVisibility(true);
      win.webContents.send('toggle-titlebar', true);

      // 短暂延迟后加载 URL，确保 dragarea 已显示
      setTimeout(() => {
        win.webContents.send('load-url', url);
      }, 50);
    }
  });
});

// 处理上下文菜单
ipcMain.on('show-context-menu', (event, params) => {
  const window = BrowserWindow.fromWebContents(event.sender);
  console.log('Opening context menu for URL:', params.linkURL);

  const template = [
    {
      label: '在新窗口中打开',
      click: () => {
        console.log('Creating new window for URL:', params.linkURL);
        const win = createWindow();

        // 存储要加载的URL
        win.urlToLoad = params.linkURL;

        // 监听渲染进程准备就绪的消息
        ipcMain.once('window-ready', (readyEvent) => {
          // 确保消息来自正确的窗口
          if (readyEvent.sender === win.webContents) {
            console.log('Window signaled ready, sending URL:', params.linkURL);

            // 先显示 dragarea，然后加载 URL
            win.windowState.isTrafficLightVisible = true;
            win.setWindowButtonVisibility(true);
            win.webContents.send('toggle-titlebar', true);

            // 短暂延迟后加载 URL，确保 dragarea 已显示
            setTimeout(() => {
              win.webContents.send('load-url', params.linkURL);
            }, 50);
          }
        });
      }
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  menu.popup({ window, x: params.x, y: params.y });
});

// 处理可点击元素的上下文菜单（没有直接URL的元素）
ipcMain.on('show-clickable-context-menu', (event, params) => {
  const window = BrowserWindow.fromWebContents(event.sender);
  console.log('Opening context menu for clickable element:', params.elementInfo);

  const template = [
    {
      label: '在新窗口中打开',
      click: () => {
        console.log('Creating new window for clickable element');
        const win = createWindow();

        // 创建新窗口后，发送指令到渲染进程，让它执行点击该元素的操作，并捕获页面跳转
        ipcMain.once('window-ready', (readyEvent) => {
          if (readyEvent.sender === win.webContents) {
            // 发送消息给原始窗口，让它模拟点击元素并获取URL
            event.sender.send('simulate-click-and-capture-navigation', {
              x: params.x,
              y: params.y,
              newWindowId: win.id
            });
          }
        });
      }
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  menu.popup({ window, x: params.x, y: params.y });
});

// 处理在捕获的URL加载到新窗口
ipcMain.on('load-captured-url-in-new-window', (event, data) => {
  console.log('Loading captured URL in new window:', data.url);

  // 查找新窗口
  const win = BrowserWindow.fromId(data.newWindowId);
  if (win && !win.isDestroyed()) {
    if (data.url) {
      // 如果有有效的URL
      console.log('Loading captured URL in window:', data.url);

      // 使用 loadURL 而不是发送消息 - 确保URL有效
      if (data.url.startsWith('http://') || data.url.startsWith('https://')) {
        win.webContents.send('load-url', data.url);
      } else {
        // 如果URL不是 http/https 开头，尝试添加 http://
        const fixedUrl = data.url.startsWith('//') ?
          `https:${data.url}` :
          (data.url.match(/^[a-zA-Z0-9]/) ? `http://${data.url}` : data.url);

        if (fixedUrl.startsWith('http://') || fixedUrl.startsWith('https://')) {
          win.webContents.send('load-url', fixedUrl);
        } else {
          // 如果无法修复URL，关闭窗口
          console.error('无法加载无效URL:', data.url);
          win.close();
        }
      }
    } else {
      // 如果没有URL，关闭窗口
      console.error('捕获的URL无效，关闭窗口');
      win.close();
    }
  } else {
    console.error('New window not found or destroyed');
  }
});

// 处理关闭空窗口（当无法捕获点击URL时）
ipcMain.on('close-empty-window', (event, windowId) => {
  console.log('Closing empty window:', windowId);

  // 查找窗口
  const win = BrowserWindow.fromId(windowId);
  if (win && !win.isDestroyed()) {
    // 关闭空窗口
    win.close();
  }
});

process.on('unhandledRejection', (error) => {
  console.error('Unhandled promise rejection:', error)
})

// 创建下载管理器窗口
function createDownloadWindow() {
  // 检查是否已经有一个下载管理器窗口正在创建或已存在
  if (downloadWindow) {
    if (!downloadWindow.isDestroyed()) {
      if (downloadWindow.isMinimized()) {
        downloadWindow.restore();
      }
      downloadWindow.show();
      downloadWindow.focus();
    } else {
      downloadWindow = null;
    }
    return;
  }

  // 获取主屏幕的尺寸
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

  // 设置下载窗口的尺寸
  const windowWidth = 800;
  const windowHeight = 600;

  // 计算窗口位置，使其位于右下角
  const xPosition = screenWidth - windowWidth;
  const yPosition = screenHeight - windowHeight;

  downloadWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    x: xPosition,
    y: yPosition,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    title: '下载管理器'
  });

  downloadWindow.loadFile('downloads.html');

  // 在窗口加载完成后同步现有下载
  downloadWindow.webContents.on('did-finish-load', () => {
    console.log('下载管理器窗口加载完成，同步现有下载...');
    console.log('当前下载任务数:', downloads.size);

    // 遍历所有正在进行的下载，只同步活跃的下载项
    downloads.forEach((item, id) => {
      try {
        const state = item.getState();
        // 只同步未取消、未完成且未中断的下载
        if (state !== 'cancelled' && state !== 'completed' && state !== 'interrupted') {
          console.log('同步下载项:', item.getFilename());

          const isPaused = item.isPaused();
          const receivedBytes = item.getReceivedBytes();
          const totalBytes = item.getTotalBytes();
          const progress = totalBytes > 0 ? (receivedBytes * 100 / totalBytes).toFixed(2) : 0;

          // 发送下载开始事件，包含状态和进度信息
          downloadWindow.webContents.send('download-started', {
            id: id,
            filename: item.getFilename(),
            url: item.getURL(),
            totalBytes: totalBytes,
            state: isPaused ? 'paused' : state,
            progress: parseFloat(progress)
          });

          // 如果下载已暂停，发送暂停状态
          if (isPaused) {
            downloadWindow.webContents.send('download-state', {
              id: id,
              state: '下载已暂停',
              filename: item.getFilename()
            });

            // 同时发送进度信息
            downloadWindow.webContents.send('download-progress', {
              id: id,
              percent: progress,
              transferred: receivedBytes,
              total: totalBytes,
              state: 'paused',
              filename: item.getFilename()
            });
          }
          // 如果下载正在进行且有进度，发送进度信息
          else if (state === 'progressing' && totalBytes > 0) {
            downloadWindow.webContents.send('download-progress', {
              id: id,
              percent: progress,
              transferred: receivedBytes,
              total: totalBytes,
              state: state,
              filename: item.getFilename()
            });
          }
        }
      } catch (error) {
        console.log('无法获取下载状态:', error);
      }
    });
  });

  downloadWindow.on('closed', () => {
    downloadWindow = null;
  });
}

// 处理下载控制事件
ipcMain.on('pause-download', async (event, id) => {
    const item = downloads.get(id);
    if (item) {
        try {
            // 暂停下载
            await item.pause();

            // 确保下载真的暂停了
            await new Promise(resolve => setTimeout(resolve, 100));

            // 发送暂停状态
            if (downloadWindow && !downloadWindow.isDestroyed()) {
                // 先发送进度更新
                const receivedBytes = item.getReceivedBytes();
                const totalBytes = item.getTotalBytes();
                const progress = totalBytes ? (receivedBytes * 100 / totalBytes).toFixed(2) : 0;

                downloadWindow.webContents.send('download-progress', {
                    id: id,
                    percent: progress,
                    transferred: receivedBytes,
                    total: totalBytes,
                    state: 'paused'
                });

                // 再发送状态更新
                downloadWindow.webContents.send('download-state', {
                    id: id,
                    state: '下载已暂停',
                    filename: item.getFilename()
                });
            }
        } catch (error) {
            console.error('Error pausing download:', error);
        }
    }
});

ipcMain.on('resume-download', (event, id) => {
  const item = downloads.get(id);
  if (item) {
    item.resume();
  }
});

ipcMain.on('cancel-download', async (event, id) => {
  const item = downloads.get(id);
  if (item) {
    try {
      // 检查下载项的当前状态
      const state = item.getState();

      // 如果下载已经完成或已经取消，则不执行取消操作
      if (state === 'completed' || state === 'cancelled') {
        console.log('下载已经完成或已取消，无需再次取消');
        return;
      }

      // 执行取消操作
      await item.cancel();

      // 确保取消操作完成后再发送状态更新
      await new Promise(resolve => setTimeout(resolve, 100));

      // 发送取消状态到下载管理器窗口
      if (downloadWindow && !downloadWindow.isDestroyed()) {
        downloadWindow.webContents.send('download-state', {
          id: id,
          state: '下载已取消',
          filename: item.getFilename()
        });
      }

      // 从下载列表中移除
      downloads.delete(id);
    } catch (error) {
      console.error('取消下载时出错:', error);
      // 发送错误状态到下载管理器窗口
      if (downloadWindow && !downloadWindow.isDestroyed()) {
        downloadWindow.webContents.send('download-state', {
          id: id,
          state: '取消下载失败',
          filename: item.getFilename()
        });
      }
    }
  }
});

// 处理打开下载目录的请求
ipcMain.on('open-download-directory', (event, id) => {
  const item = downloads.get(id);
  if (item) {
    shell.showItemInFolder(item.getSavePath());
  } else {
    // Check if we have the path stored for completed downloads
    const completedPath = completedDownloadPaths.get(id);
    if (completedPath) {
      shell.showItemInFolder(completedPath);
    } else {
      // If no path is found, fall back to downloads directory
      const downloadPath = app.getPath('downloads');
      shell.showItemInFolder(downloadPath);
    }
  }
});

function createBookmarksWindow() {
  if (bookmarksWindow) {
    bookmarksWindow.show();
    return;
  }

  bookmarksWindow = new BrowserWindow({
    width: 800,
    height: 600,
    frame: false,
    transparent: true,  // 添加透明支持
    parent: BrowserWindow.getFocusedWindow(),
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  bookmarksWindow.loadFile('bookmarks-modal.html');

  // 添加失去焦点时自动关闭
  bookmarksWindow.on('blur', () => {
    if (bookmarksWindow) {
      bookmarksWindow.close();
    }
  });

  bookmarksWindow.on('closed', () => {
    bookmarksWindow = null;
  });
}

// Add IPC handler for loading URLs from bookmarks
ipcMain.on('load-url', (event, url) => {
  // 获取书签窗口的父窗口
  const parentWindow = BrowserWindow.fromWebContents(event.sender).getParentWindow();
  if (parentWindow) {
    parentWindow.webContents.send('load-url', url);
    // Close the bookmarks window if it exists
    if (bookmarksWindow) {
      bookmarksWindow.close();
    }
  }
});

// 处理保存书签的请求
ipcMain.handle('save-bookmark', async (event, bookmark) => {
  try {
    const bookmarks = await readBookmarks()
    // 检查是否已存在
    const exists = bookmarks.some(b => b.url === bookmark.url)
    if (!exists) {
      bookmarks.push(bookmark)  // 使用 push 替代 unshift
      // 限制最大数量
      if (bookmarks.length > MAX_BOOKMARKS) {
        bookmarks.shift()  // 移除最老的书签，而不是最新的
      }
      await saveBookmarks(bookmarks)
      return { success: true }
    }
    return { success: false, reason: 'already_exists' }
  } catch (error) {
    console.error('Error saving bookmark:', error)
    return { success: false, reason: 'error', message: error.message }
  }
})

// 处理获取书签的请求
ipcMain.handle('get-bookmarks', async () => {
  try {
    const bookmarks = await readBookmarks()
    return { success: true, bookmarks }
  } catch (error) {
    console.error('Error reading bookmarks:', error)
    return { success: false, reason: 'error', message: error.message }
  }
})

// 处理删除书签的请求
ipcMain.handle('delete-bookmark', async (event, url) => {
  try {
    const bookmarks = await readBookmarks()
    const newBookmarks = bookmarks.filter(b => b.url !== url)
    await saveBookmarks(newBookmarks)
    return { success: true }
  } catch (error) {
    console.error('Error deleting bookmark:', error)
    return { success: false, reason: 'error', message: error.message }
  }
})

let toastWindow = null;

function showToast() {
  // 如果已经有toast窗口，先关闭它
  if (toastWindow && !toastWindow.isDestroyed()) {
    toastWindow.close();
  }

  const parentWindow = BrowserWindow.getFocusedWindow();
  if (!parentWindow) return;

  const [parentX, parentY] = parentWindow.getPosition();
  const [parentWidth, parentHeight] = parentWindow.getSize();

  toastWindow = new BrowserWindow({
    width: 200,
    height: 80,
    frame: false,
    transparent: true,
    resizable: false,
    skipTaskbar: true,
    show: false,
    parent: parentWindow,
    modal: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  // 计算位置使toast显示在父窗口的右上角
  const x = parentX + parentWidth - 220;
  const y = parentY + 20;
  toastWindow.setPosition(x, y);

  toastWindow.loadFile('toast-modal.html');
  toastWindow.once('ready-to-show', () => {
    toastWindow.show();
  });

  // 3秒后自动关闭
  setTimeout(() => {
    if (toastWindow && !toastWindow.isDestroyed()) {
      toastWindow.close();
    }
  }, 3000);

  toastWindow.on('closed', () => {
    toastWindow = null;
  });
}

// 处理获取webview信息的响应
ipcMain.on('webview-info', async (event, info) => {
  const focusedWindow = BrowserWindow.getFocusedWindow();
  if (!focusedWindow) return;

  try {
    // 获取所有webContents
    const allWebContents = webContents.getAllWebContents();

    // 找到对应的webview webContents
    const webviewContents = allWebContents.find(contents =>
      contents.getURL() === info.url && contents.getType() === 'webview'
    );

    if (webviewContents) {
      // 强制停止加载
      webviewContents.stop();

      // 清除缓存
      await webviewContents.session.clearCache();

      // 强制重新加载，忽略缓存
      await webviewContents.reloadIgnoringCache();

      // 通知渲染进程重新加载完成
      focusedWindow.webContents.send('force-refresh-complete');
    }
  } catch (error) {
    console.error('Error during force refresh:', error);
    // 如果上面的方法失败，尝试使用备用方法
    focusedWindow.webContents.send('force-refresh-fallback');
  }
});

// 处理强制导航到新URL
ipcMain.on('force-navigate', async (event, url) => {
  const focusedWindow = BrowserWindow.getFocusedWindow();
  if (!focusedWindow) return;

  try {
    // 获取所有webContents
    const allWebContents = webContents.getAllWebContents();

    // 找到webview的webContents
    const webviewContents = allWebContents.find(contents =>
      contents.getType() === 'webview'
    );

    if (webviewContents) {
      // 强制停止当前加载
      webviewContents.stop();

      // 清除缓存
      await webviewContents.session.clearCache();

      // 加载新URL
      await webviewContents.loadURL(url);
    }
  } catch (error) {
    console.error('Error during force navigation:', error);
    // 如果上面的方法失败，尝试使用备用方法
    focusedWindow.webContents.send('force-navigate-fallback', url);
  }
});

// 在应用退出时移除注销快捷键
app.on('will-quit', () => {
  // 不再需要注销全局快捷键
  // unregisterEscapeKey();
});

// 添加一个函数来确定窗口类型
function isPopupWindow(browserWindow) {
  // 如果在主窗口列表中，则不是弹出窗口
  if (mainWindowIds.has(browserWindow.id)) {
    return false;
  }

  // 否则，如果是通过window.open创建的或其他条件，则是弹出窗口
  return true;
}

// 密码管理功能
// 确保密码文件存在
async function ensurePasswordsFile() {
  try {
    await fs.access(passwordsPath)
  } catch {
    // 如果文件不存在，创建一个空的密码文件
    await fs.writeFile(passwordsPath, '[]', 'utf8')
  }
}

// 读取密码
async function readPasswords() {
  try {
    await ensurePasswordsFile()
    const data = await fs.readFile(passwordsPath, 'utf8')
    return JSON.parse(data)
  } catch {
    return []
  }
}

// 保存密码
async function savePassword(domain, username, password) {
  try {
    const passwords = await readPasswords()
    
    // 检查是否已存在该域名的密码
    const existingIndex = passwords.findIndex(p => p.domain === domain && p.username === username)
    
    const passwordEntry = {
      domain: domain,
      username: username,
      password: Buffer.from(password).toString('base64'), // base64加密
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    if (existingIndex >= 0) {
      // 更新现有密码
      passwords[existingIndex] = passwordEntry
    } else {
      // 添加新密码
      passwords.push(passwordEntry)
    }
    
    await fs.writeFile(passwordsPath, JSON.stringify(passwords, null, 2), 'utf8')
    return { success: true }
  } catch (error) {
    console.error('Error saving password:', error)
    return { success: false, error: error.message }
  }
}

// 获取密码
async function getPassword(domain) {
  try {
    const passwords = await readPasswords()
    
    // 查找匹配的密码（支持模糊匹配域名）
    const matchedPasswords = passwords.filter(p => {
      return domain.includes(p.domain) || p.domain.includes(domain)
    })
    
    // 返回解密后的密码
    return matchedPasswords.map(p => ({
      domain: p.domain,
      username: p.username,
      password: Buffer.from(p.password, 'base64').toString(), // base64解密
      createdAt: p.createdAt,
      updatedAt: p.updatedAt
    }))
  } catch (error) {
    console.error('Error getting password:', error)
    return []
  }
}

// 删除密码
async function deletePassword(domain, username) {
  try {
    const passwords = await readPasswords()
    const filteredPasswords = passwords.filter(p => !(p.domain === domain && p.username === username))
    
    await fs.writeFile(passwordsPath, JSON.stringify(filteredPasswords, null, 2), 'utf8')
    return { success: true }
  } catch (error) {
    console.error('Error deleting password:', error)
    return { success: false, error: error.message }
  }
}

// 密码管理相关的IPC处理器
// 保存密码
ipcMain.handle('save-password', async (event, domain, username, password) => {
  return await savePassword(domain, username, password)
})

// 获取密码
ipcMain.handle('get-passwords', async (event, domain) => {
  return await getPassword(domain)
})

// 删除密码
ipcMain.handle('delete-password', async (event, domain, username) => {
  return await deletePassword(domain, username)
})